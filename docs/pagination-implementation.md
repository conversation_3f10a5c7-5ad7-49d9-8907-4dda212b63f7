# 管理员密钥列表分页功能实现文档

## 概述

为 `/api/admin/key/list` 接口实现了分页功能，支持高效的数据查询和展示，同时保持向后兼容性。

## 功能特性

### 1. 分页参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `pagination` | boolean | true | 是否启用分页 |
| `page` | integer | 1 | 页码（从1开始） |
| `pageSize` | integer | 10 | 每页大小（1-100） |
| `onlyActive` | boolean | true | 是否只返回有效密钥 |

### 2. 响应格式

#### 分页响应（pagination=true）
```json
{
  "code": 200,
  "message": "获取管理员密钥列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "code": "**********",
        "maxCNYLimit": 10000,
        "status": "ACTIVE",
        "accountType": "FORMAL",
        // ... 其他字段
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

#### 非分页响应（pagination=false）
```json
{
  "code": 200,
  "message": "获取管理员密钥列表成功",
  "data": [
    {
      "id": 1,
      "code": "**********",
      // ... 管理员密钥对象
    }
  ]
}
```

## 实现细节

### 1. 数据结构

#### PaginationInfo
```kotlin
@Serializable
data class PaginationInfo(
    val page: Int,          // 当前页码（从1开始）
    val pageSize: Int,      // 每页大小
    val total: Long,        // 总记录数
    val totalPages: Int     // 总页数
)
```

#### PaginatedResponseData
```kotlin
@Serializable
data class PaginatedResponseData<T>(
    val items: List<T>,
    val pagination: PaginationInfo
)
```

### 2. 服务层实现

在 `AdminKeyService` 中新增 `listAdminKeysPaginated` 方法：

```kotlin
fun listAdminKeysPaginated(
    onlyActive: Boolean = true, 
    page: Int = 1, 
    pageSize: Int = 10
): Pair<List<AdminKey>, Long>
```

- 使用 SQL `LIMIT` 和 `OFFSET` 实现分页
- 按创建时间降序排列（最新的在前）
- 返回数据列表和总记录数

### 3. 路由层实现

在 `AdminKeyManagerRouter` 中更新 `/list` 端点：

- 解析查询参数并验证
- 根据 `pagination` 参数选择响应格式
- 参数验证和默认值处理

## API 使用示例

### 基本分页查询
```http
GET /api/admin/key/list?page=1&pageSize=10
Authorization: Bearer {token}
```

### 获取所有有效密钥（第2页）
```http
GET /api/admin/key/list?onlyActive=true&page=2&pageSize=5
Authorization: Bearer {token}
```

### 禁用分页（向后兼容）
```http
GET /api/admin/key/list?pagination=false
Authorization: Bearer {token}
```

### 获取所有状态的密钥
```http
GET /api/admin/key/list?onlyActive=false&page=1&pageSize=20
Authorization: Bearer {token}
```

## 参数验证

- `page`: 小于1时默认为1
- `pageSize`: 超出1-100范围时默认为10
- 无效参数会使用默认值，不会返回错误

## 性能优化

1. **数据库层面**：
   - 使用 `LIMIT` 和 `OFFSET` 减少数据传输
   - 按创建时间索引排序

2. **应用层面**：
   - 分离计数查询和数据查询
   - 避免加载不必要的数据

## 向后兼容性

- 默认启用分页（`pagination=true`）
- 设置 `pagination=false` 可获得原始数组格式响应
- 现有客户端无需修改即可继续使用

## OpenAPI 文档更新

已更新 `src/main/resources/openapi/documentation.yaml`：

1. 添加新的查询参数文档
2. 定义分页相关的数据结构
3. 更新响应格式说明
4. 提供使用示例

## 测试用例

创建了 `test-requests/admin-key-pagination.http` 包含：

- 默认分页测试
- 自定义页码和页大小测试
- 边界值测试
- 向后兼容性测试
- 参数验证测试

## 部署注意事项

1. 确保数据库连接池能处理并发查询
2. 监控大页面查询的性能影响
3. 考虑为创建时间字段添加索引以优化排序性能

## 未来扩展

1. 可考虑添加排序参数支持
2. 可添加搜索和过滤功能
3. 可实现基于游标的分页以提高大数据集性能
