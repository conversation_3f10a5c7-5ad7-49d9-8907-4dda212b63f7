package com.dzhp.permit.routers.zjjcfzInteral

import com.dzhp.permit.models.AdminKey
import com.dzhp.permit.models.AdminKeyStatus
import com.dzhp.permit.models.AdminAccountType
import com.dzhp.permit.models.StandardResponseStruct
import com.dzhp.permit.models.PaginatedResponseData
import com.dzhp.permit.models.PaginationInfo
import com.dzhp.permit.services.AdminKeyService
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.*
import io.ktor.server.auth.authenticate
import io.ktor.server.auth.jwt.JWTPrincipal
import io.ktor.server.auth.principal
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.*
import org.slf4j.LoggerFactory

/**
 * 管理员密钥相关路由
 *
 * - POST /api/admin/key/new : 创建新的管理员密钥，返回主键 id 和生成的密钥
 * - GET /api/admin/key/list : 获取管理员密钥列表（支持分页）
 *   查询参数：
 *   - onlyActive: 是否只返回有效密钥（默认true）
 *   - pagination: 是否使用分页（默认true）
 *   - page: 页码，从1开始（默认1）
 *   - pageSize: 每页大小，1-100（默认10）
 * - GET /api/admin/key/{id} : 获取指定管理员密钥详情
 * - PUT /api/admin/key/{id} : 更新管理员密钥信息（不可修改：code, expiresAt, province, city, district）
 * - PUT /api/admin/key/{id}/status : 更新管理员密钥状态
 * - DELETE /api/admin/key/{id} : 删除管理员密钥
 */
fun Application.adminKeyRouter(adminKeyService: AdminKeyService) {
    val logger = LoggerFactory.getLogger("AdminKeyRouter")

    routing {
        authenticate("admin-jwt") {
            route("/api/admin/key") {
                /** 创建新的管理员密钥 */
                post("/new") {
                    try {
                        // 接收请求数据
                        val request = call.receive<Map<String, JsonElement>>()

                        val principal = call.principal<JWTPrincipal>()
                        val adminUsername = principal?.getClaim("adminUsername", String::class)

                        // 解析必要字段
                        val maxCNYLimit = request["maxCNYLimit"]?.jsonPrimitive?.doubleOrNull ?: 10000.0
                        val companyName = request["companyName"]?.jsonPrimitive?.contentOrNull ?: "默认公司"

                        // 解析可选字段
                        val status = request["status"]?.jsonPrimitive?.contentOrNull?.let {
                            try { AdminKeyStatus.valueOf(it) } catch (e: Exception) { AdminKeyStatus.ACTIVE }
                        } ?: AdminKeyStatus.ACTIVE

                        val accountType = request["accountType"]?.jsonPrimitive?.contentOrNull?.let {
                            try { AdminAccountType.valueOf(it) } catch (e: Exception) { AdminAccountType.FORMAL }
                        } ?: AdminAccountType.FORMAL

                        val province = call.request.queryParameters["province"]
                            ?: request["province"]?.jsonPrimitive?.contentOrNull
                            ?: "北京市"

                        val city = call.request.queryParameters["city"]
                            ?: request["city"]?.jsonPrimitive?.contentOrNull
                            ?: "北京市"

                        val district = call.request.queryParameters["district"]
                            ?: request["district"]?.jsonPrimitive?.contentOrNull
                            ?: "东城区"

                        val contractNumber = request["contractNumber"]?.jsonPrimitive?.contentOrNull
                        val contactPerson = request["contactPerson"]?.jsonPrimitive?.contentOrNull
                        val contactPhone = request["contactPhone"]?.jsonPrimitive?.contentOrNull
                        val contactEmail = request["contactEmail"]?.jsonPrimitive?.contentOrNull

                        // 解析时间相关字段
                        val currentTime = System.currentTimeMillis()
                        val defaultExpiresAt = currentTime + (365 * 24 * 60 * 60 * 1000L) // 默认一年后过期
                        val expiresAt = request["expiresAt"]?.jsonPrimitive?.longOrNull?.takeIf { it > currentTime } ?: defaultExpiresAt

                        // 自动生成管理员密钥，不接受用户自定义的密钥
                        val codeToUse = adminKeyService.generateRandomAdminKey(province, city, district)

                        // 创建带有完整密钥的对象
                        val completeAdminKey = AdminKey(
                            code = codeToUse,
                            maxCNYLimit = maxCNYLimit,
                            status = status,
                            accountType = accountType,
                            province = province,
                            city = city,
                            district = district,
                            companyName = companyName,
                            contractNumber = contractNumber,
                            contactPerson = contactPerson,
                            contactPhone = contactPhone,
                            contactEmail = contactEmail,
                            expiresAt = expiresAt,
                            createdAt = currentTime,
                            updatedAt = currentTime,

                            createBy = adminUsername,
                            updateBy = adminUsername
                        )

                        // 创建密钥并获取ID
                        val id = adminKeyService.createAdminKey(completeAdminKey)

                        // 返回成功响应
                        call.respond(
                            HttpStatusCode.Created,
                            StandardResponseStruct(
                                code = HttpStatusCode.Created.value,
                                message = "管理员密钥创建成功",
                                data = JsonObject(mapOf(
                                    "id" to JsonPrimitive(id),
                                    "code" to JsonPrimitive(codeToUse)
                                ))
                            )
                        )
                    } catch (e: Exception) {
                        logger.error("创建管理员密钥失败", e)
                        call.respond(
                            HttpStatusCode.InternalServerError,
                            StandardResponseStruct(
                                code = HttpStatusCode.InternalServerError.value,
                                message = "创建管理员密钥失败: ${e.message}",
                                data = null
                            )
                        )
                    }
                }

                /** 获取管理员密钥列表 */
                get("/list") {
                    try {
                        // 获取查询参数
                        val onlyActive = call.request.queryParameters["onlyActive"]?.toBoolean() ?: true
                        val page = call.request.queryParameters["page"]?.toIntOrNull()?.takeIf { it > 0 } ?: 1
                        val pageSize = call.request.queryParameters["pageSize"]?.toIntOrNull()?.takeIf { it in 1..100 } ?: 10
                        val usePagination = call.request.queryParameters["pagination"]?.toBoolean() ?: true

                        if (usePagination) {
                            // 使用分页查询
                            val (adminKeys, total) = adminKeyService.listAdminKeysPaginated(onlyActive, page, pageSize)

                            // 计算总页数
                            val totalPages = if (total == 0L) 0 else ((total - 1) / pageSize + 1).toInt()

                            // 构建分页响应数据
                            val items = adminKeys.map { key ->
                                JsonObject(mapOf(
                                    "id" to JsonPrimitive(key.id),
                                    "code" to JsonPrimitive(key.code),
                                    "maxCNYLimit" to JsonPrimitive(key.maxCNYLimit),
                                    "status" to JsonPrimitive(key.status.toString()),
                                    "accountType" to JsonPrimitive(key.accountType.toString()),

                                    // 地域信息
                                    "province" to (key.province?.let { JsonPrimitive(it) } ?: JsonNull),
                                    "city" to (key.city?.let { JsonPrimitive(it) } ?: JsonNull),
                                    "district" to (key.district?.let { JsonPrimitive(it) } ?: JsonNull),

                                    // 单位和联系信息
                                    "companyName" to JsonPrimitive(key.companyName),
                                    "contractNumber" to (key.contractNumber?.let { JsonPrimitive(it) } ?: JsonNull),
                                    "contactPerson" to (key.contactPerson?.let { JsonPrimitive(it) } ?: JsonNull),
                                    "contactPhone" to (key.contactPhone?.let { JsonPrimitive(it) } ?: JsonNull),
                                    "contactEmail" to (key.contactEmail?.let { JsonPrimitive(it) } ?: JsonNull),

                                    // 有效期
                                    "expiresAt" to JsonPrimitive(key.expiresAt),

                                    "createBy" to (key.createBy?.let { JsonPrimitive(it) } ?: JsonNull),
                                    "updateBy" to (key.updateBy?.let { JsonPrimitive(it) } ?: JsonNull),
                                    "deleted" to JsonPrimitive(key.deleted),
                                    "createdAt" to JsonPrimitive(key.createdAt),
                                    "updatedAt" to JsonPrimitive(key.updatedAt)
                                ))
                            }

                            val paginatedData = PaginatedResponseData(
                                items = items,
                                pagination = PaginationInfo(
                                    page = page,
                                    pageSize = pageSize,
                                    total = total,
                                    totalPages = totalPages
                                )
                            )

                            // 返回分页响应
                            call.respond(
                                HttpStatusCode.OK,
                                StandardResponseStruct(
                                    code = HttpStatusCode.OK.value,
                                    message = "获取管理员密钥列表成功",
                                    data = paginatedData
                                )
                            )
                        } else {
                            // 不使用分页，返回所有数据（保持向后兼容）
                            val adminKeys = adminKeyService.listAdminKeys(onlyActive)

                            // 构建JSON响应
                            val jsonArray = JsonArray(adminKeys.map { key ->
                                JsonObject(mapOf(
                                    "id" to JsonPrimitive(key.id),
                                    "code" to JsonPrimitive(key.code),
                                    "maxCNYLimit" to JsonPrimitive(key.maxCNYLimit),
                                    "status" to JsonPrimitive(key.status.toString()),
                                    "accountType" to JsonPrimitive(key.accountType.toString()),

                                    // 地域信息
                                    "province" to (key.province?.let { JsonPrimitive(it) } ?: JsonNull),
                                    "city" to (key.city?.let { JsonPrimitive(it) } ?: JsonNull),
                                    "district" to (key.district?.let { JsonPrimitive(it) } ?: JsonNull),

                                    // 单位和联系信息
                                    "companyName" to JsonPrimitive(key.companyName),
                                    "contractNumber" to (key.contractNumber?.let { JsonPrimitive(it) } ?: JsonNull),
                                    "contactPerson" to (key.contactPerson?.let { JsonPrimitive(it) } ?: JsonNull),
                                    "contactPhone" to (key.contactPhone?.let { JsonPrimitive(it) } ?: JsonNull),
                                    "contactEmail" to (key.contactEmail?.let { JsonPrimitive(it) } ?: JsonNull),

                                    // 有效期
                                    "expiresAt" to JsonPrimitive(key.expiresAt),

                                    "createBy" to (key.createBy?.let { JsonPrimitive(it) } ?: JsonNull),
                                    "updateBy" to (key.updateBy?.let { JsonPrimitive(it) } ?: JsonNull),
                                    "deleted" to JsonPrimitive(key.deleted),
                                    "createdAt" to JsonPrimitive(key.createdAt),
                                    "updatedAt" to JsonPrimitive(key.updatedAt)
                                ))
                            })

                            // 返回成功响应
                            call.respond(
                                HttpStatusCode.OK,
                                StandardResponseStruct(
                                    code = HttpStatusCode.OK.value,
                                    message = "获取管理员密钥列表成功",
                                    data = jsonArray
                                )
                            )
                        }
                    } catch (e: Exception) {
                        logger.error("获取管理员密钥列表失败", e)
                        call.respond(
                            HttpStatusCode.InternalServerError,
                            StandardResponseStruct(
                                code = HttpStatusCode.InternalServerError.value,
                                message = "获取管理员密钥列表失败: ${e.message}",
                                data = null
                            )
                        )
                    }
                }

                /** 获取指定管理员密钥详情 */
                get("/{id}") {
                    try {
                        val id = call.parameters["id"]?.toLongOrNull()
                            ?: return@get call.respond(
                                HttpStatusCode.BadRequest,
                                StandardResponseStruct(
                                    code = HttpStatusCode.BadRequest.value,
                                    message = "无效的管理员密钥ID",
                                    data = null
                                )
                            )

                        val adminKey = adminKeyService.findAdminKeyById(id)
                            ?: return@get call.respond(
                                HttpStatusCode.NotFound,
                                StandardResponseStruct(
                                    code = HttpStatusCode.NotFound.value,
                                    message = "管理员密钥不存在",
                                    data = null
                                )
                            )

                        // 构建JSON响应
                        val jsonObject = JsonObject(mapOf(
                            "id" to JsonPrimitive(adminKey.id),
                            "code" to JsonPrimitive(adminKey.code),
                            "maxCNYLimit" to JsonPrimitive(adminKey.maxCNYLimit),
                            "status" to JsonPrimitive(adminKey.status.toString()),
                            "accountType" to JsonPrimitive(adminKey.accountType.toString()),

                            // 地域信息
                            "province" to (adminKey.province?.let { JsonPrimitive(it) } ?: JsonNull),
                            "city" to (adminKey.city?.let { JsonPrimitive(it) } ?: JsonNull),
                            "district" to (adminKey.district?.let { JsonPrimitive(it) } ?: JsonNull),

                            // 单位和联系信息
                            "companyName" to JsonPrimitive(adminKey.companyName),
                            "contractNumber" to (adminKey.contractNumber?.let { JsonPrimitive(it) } ?: JsonNull),
                            "contactPerson" to (adminKey.contactPerson?.let { JsonPrimitive(it) } ?: JsonNull),
                            "contactPhone" to (adminKey.contactPhone?.let { JsonPrimitive(it) } ?: JsonNull),
                            "contactEmail" to (adminKey.contactEmail?.let { JsonPrimitive(it) } ?: JsonNull),

                            // 有效期
                            "expiresAt" to JsonPrimitive(adminKey.expiresAt),

                            "createBy" to (adminKey.createBy?.let { JsonPrimitive(it) } ?: JsonNull),
                            "updateBy" to (adminKey.updateBy?.let { JsonPrimitive(it) } ?: JsonNull),
                            "deleted" to JsonPrimitive(adminKey.deleted),
                            "createdAt" to JsonPrimitive(adminKey.createdAt),
                            "updatedAt" to JsonPrimitive(adminKey.updatedAt)
                        ))

                        // 返回成功响应
                        call.respond(
                            HttpStatusCode.OK,
                            StandardResponseStruct(
                                code = HttpStatusCode.OK.value,
                                message = "获取管理员密钥详情成功",
                                data = jsonObject
                            )
                        )
                    } catch (e: Exception) {
                        logger.error("获取管理员密钥详情失败", e)
                        call.respond(
                            HttpStatusCode.InternalServerError,
                            StandardResponseStruct(
                                code = HttpStatusCode.InternalServerError.value,
                                message = "获取管理员密钥详情失败: ${e.message}",
                                data = null
                            )
                        )
                    }
                }

                /** 更新管理员密钥状态 */
                @Serializable
                data class UpdateStatusRequest(
                    val status: String
                )

                put("/{id}/status") {
                    try {
                        val id = call.parameters["id"]?.toLongOrNull()
                            ?: return@put call.respond(
                                HttpStatusCode.BadRequest,
                                StandardResponseStruct(
                                    code = HttpStatusCode.BadRequest.value,
                                    message = "无效的管理员密钥ID",
                                    data = null
                                )
                            )

                        val request = call.receive<UpdateStatusRequest>()
                        val status = try {
                            AdminKeyStatus.valueOf(request.status)
                        } catch (_: IllegalArgumentException) {
                            return@put call.respond(
                                HttpStatusCode.BadRequest,
                                StandardResponseStruct(
                                    code = HttpStatusCode.BadRequest.value,
                                    message = "无效的状态值，有效值为: ${AdminKeyStatus.entries.joinToString()}",
                                    data = null
                                )
                            )
                        }

                        val success = adminKeyService.updateAdminKeyStatus(id, status)
                        if (success) {
                            call.respond(
                                HttpStatusCode.OK,
                                StandardResponseStruct(
                                    code = HttpStatusCode.OK.value,
                                    message = "管理员密钥状态更新成功",
                                    data = null
                                )
                            )
                        } else {
                            call.respond(
                                HttpStatusCode.NotFound,
                                StandardResponseStruct(
                                    code = HttpStatusCode.NotFound.value,
                                    message = "管理员密钥不存在或已删除",
                                    data = null
                                )
                            )
                        }
                    } catch (e: Exception) {
                        logger.error("更新管理员密钥状态失败", e)
                        call.respond(
                            HttpStatusCode.InternalServerError,
                            StandardResponseStruct(
                                code = HttpStatusCode.InternalServerError.value,
                                message = "更新管理员密钥状态失败: ${e.message}",
                                data = null
                            )
                        )
                    }
                }

                /** 更新管理员密钥信息 */
                put("/{id}") {
                    try {
                        val id = call.parameters["id"]?.toLongOrNull()
                            ?: return@put call.respond(
                                HttpStatusCode.BadRequest,
                                StandardResponseStruct(
                                    code = HttpStatusCode.BadRequest.value,
                                    message = "无效的管理员密钥ID",
                                    data = null
                                )
                            )

                        // 获取当前管理员密钥信息
                        val currentAdminKey = adminKeyService.findAdminKeyById(id)
                            ?: return@put call.respond(
                                HttpStatusCode.NotFound,
                                StandardResponseStruct(
                                    code = HttpStatusCode.NotFound.value,
                                    message = "管理员密钥不存在或已删除",
                                    data = null
                                )
                            )

                        val principal = call.principal<JWTPrincipal>()
                        val adminUsername = principal?.getClaim("adminUsername", String::class)

                        // 接收请求数据
                        val request = call.receive<Map<String, JsonElement>>()

                        // 解析可修改的字段
                        val maxCNYLimit = request["maxCNYLimit"]?.jsonPrimitive?.doubleOrNull ?: currentAdminKey.maxCNYLimit
                        val status = request["status"]?.jsonPrimitive?.contentOrNull?.let {
                            try { AdminKeyStatus.valueOf(it) } catch (e: Exception) { currentAdminKey.status }
                        } ?: currentAdminKey.status
                        val accountType = request["accountType"]?.jsonPrimitive?.contentOrNull?.let {
                            try { AdminAccountType.valueOf(it) } catch (e: Exception) { currentAdminKey.accountType }
                        } ?: currentAdminKey.accountType

                        // 单位和联系信息
                        val companyName = request["companyName"]?.jsonPrimitive?.contentOrNull ?: currentAdminKey.companyName
                        val contractNumber = request["contractNumber"]?.jsonPrimitive?.contentOrNull ?: currentAdminKey.contractNumber
                        val contactPerson = request["contactPerson"]?.jsonPrimitive?.contentOrNull ?: currentAdminKey.contactPerson
                        val contactPhone = request["contactPhone"]?.jsonPrimitive?.contentOrNull ?: currentAdminKey.contactPhone
                        val contactEmail = request["contactEmail"]?.jsonPrimitive?.contentOrNull ?: currentAdminKey.contactEmail

                        // 创建更新对象，保留不可修改的字段
                        val updatedAdminKey = AdminKey(
                            id = currentAdminKey.id,
                            code = currentAdminKey.code, // 不可修改
                            maxCNYLimit = maxCNYLimit,
                            status = status,
                            accountType = accountType,

                            // 地域信息 - 不可修改
                            province = currentAdminKey.province,
                            city = currentAdminKey.city,
                            district = currentAdminKey.district,

                            // 单位和联系信息 - 可修改
                            companyName = companyName,
                            contractNumber = contractNumber,
                            contactPerson = contactPerson,
                            contactPhone = contactPhone,
                            contactEmail = contactEmail,

                            // 有效期 - 不可修改
                            expiresAt = currentAdminKey.expiresAt,

                            // 更新人和时间
                            createBy = currentAdminKey.createBy,
                            updateBy = adminUsername,
                            deleted = currentAdminKey.deleted,
                            createdAt = currentAdminKey.createdAt,
                            updatedAt = System.currentTimeMillis()
                        )

                        // 更新管理员密钥
                        val success = adminKeyService.updateAdminKey(id, updatedAdminKey, adminUsername)

                        if (success) {
                            // 获取更新后的管理员密钥
                            val updatedKey = adminKeyService.findAdminKeyById(id)

                            // 构建JSON响应
                            val jsonObject = JsonObject(mapOf(
                                "id" to JsonPrimitive(updatedKey!!.id),
                                "code" to JsonPrimitive(updatedKey.code),
                                "maxCNYLimit" to JsonPrimitive(updatedKey.maxCNYLimit),
                                "status" to JsonPrimitive(updatedKey.status.toString()),
                                "accountType" to JsonPrimitive(updatedKey.accountType.toString()),

                                // 地域信息
                                "province" to (updatedKey.province?.let { JsonPrimitive(it) } ?: JsonNull),
                                "city" to (updatedKey.city?.let { JsonPrimitive(it) } ?: JsonNull),
                                "district" to (updatedKey.district?.let { JsonPrimitive(it) } ?: JsonNull),

                                // 单位和联系信息
                                "companyName" to JsonPrimitive(updatedKey.companyName),
                                "contractNumber" to (updatedKey.contractNumber?.let { JsonPrimitive(it) } ?: JsonNull),
                                "contactPerson" to (updatedKey.contactPerson?.let { JsonPrimitive(it) } ?: JsonNull),
                                "contactPhone" to (updatedKey.contactPhone?.let { JsonPrimitive(it) } ?: JsonNull),
                                "contactEmail" to (updatedKey.contactEmail?.let { JsonPrimitive(it) } ?: JsonNull),

                                // 有效期
                                "expiresAt" to JsonPrimitive(updatedKey.expiresAt),

                                "createBy" to (updatedKey.createBy?.let { JsonPrimitive(it) } ?: JsonNull),
                                "updateBy" to (updatedKey.updateBy?.let { JsonPrimitive(it) } ?: JsonNull),
                                "createdAt" to JsonPrimitive(updatedKey.createdAt),
                                "updatedAt" to JsonPrimitive(updatedKey.updatedAt)
                            ))

                            call.respond(
                                HttpStatusCode.OK,
                                StandardResponseStruct(
                                    code = HttpStatusCode.OK.value,
                                    message = "管理员密钥更新成功",
                                    data = jsonObject
                                )
                            )
                        } else {
                            call.respond(
                                HttpStatusCode.InternalServerError,
                                StandardResponseStruct(
                                    code = HttpStatusCode.InternalServerError.value,
                                    message = "管理员密钥更新失败",
                                    data = null
                                )
                            )
                        }
                    } catch (e: Exception) {
                        logger.error("更新管理员密钥失败", e)
                        call.respond(
                            HttpStatusCode.InternalServerError,
                            StandardResponseStruct(
                                code = HttpStatusCode.InternalServerError.value,
                                message = "更新管理员密钥失败: ${e.message}",
                                data = null
                            )
                        )
                    }
                }

                /** 删除管理员密钥 */
                delete("/{id}") {
                    try {
                        val id = call.parameters["id"]?.toLongOrNull()
                            ?: return@delete call.respond(
                                HttpStatusCode.BadRequest,
                                StandardResponseStruct(
                                    code = HttpStatusCode.BadRequest.value,
                                    message = "无效的管理员密钥ID",
                                    data = null
                                )
                            )

                        val success = adminKeyService.deleteAdminKey(id)
                        if (success) {
                            call.respond(
                                HttpStatusCode.OK,
                                StandardResponseStruct(
                                    code = HttpStatusCode.OK.value,
                                    message = "管理员密钥删除成功",
                                    data = null
                                )
                            )
                        } else {
                            call.respond(
                                HttpStatusCode.NotFound,
                                StandardResponseStruct(
                                    code = HttpStatusCode.NotFound.value,
                                    message = "管理员密钥不存在或已删除",
                                    data = null
                                )
                            )
                        }
                    } catch (e: Exception) {
                        logger.error("删除管理员密钥失败", e)
                        call.respond(
                            HttpStatusCode.InternalServerError,
                            StandardResponseStruct(
                                code = HttpStatusCode.InternalServerError.value,
                                message = "删除管理员密钥失败: ${e.message}",
                                data = null
                            )
                        )
                    }
                }
            }
        }
    }
}
