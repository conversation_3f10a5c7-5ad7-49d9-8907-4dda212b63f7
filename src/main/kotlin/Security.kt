package com.dzhp.permit

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*


fun Application.configureSecurity() {
    // 提取共用的配置项
    val config = environment.config
    val realm = config.property("jwt.realm").getString()
    val secret = config.property("jwt.secret").getString()
    val audience = config.property("jwt.audience").getString()
    val domain = config.property("jwt.domain").getString()

    // 创建共用的JWT验证器
    val jwtVerifier = JWT
        .require(Algorithm.HMAC256(secret))
        .withAudience(audience)
        .withIssuer(domain)
        .build()

    authentication {
        // 普通用户JWT认证
        jwt {
            this.realm = realm
            verifier(jwtVerifier)
            validate { credential ->
                if (credential.payload.audience.contains(audience)) {
                    JWTPrincipal(credential.payload)
                } else null
            }
        }

        // 公司内部管理员JWT认证
        jwt("admin-jwt") {
            this.realm = realm
            verifier(jwtVerifier)
            validate { credential ->
                if (credential.payload.audience.contains(audience) &&
                    credential.payload.getClaim("role").asString() == "admin") {
                    JWTPrincipal(credential.payload)
                } else null
            }
        }

        // 一级邀请码（管理员密钥）JWT认证
        jwt("admin-key-jwt") {
            this.realm = realm
            verifier(jwtVerifier)
            validate { credential ->
                if (credential.payload.audience.contains(audience) &&
                    credential.payload.getClaim("role").asString() == "admin-key" &&
                    credential.payload.getClaim("adminKeyCode").asString().isNotEmpty()) {
                    JWTPrincipal(credential.payload)
                } else null
            }
        }

        // 二级邀请码用户JWT认证
        jwt("user-llm-jwt") {
            this.realm = realm
            verifier(jwtVerifier)
            validate { credential ->
                if (credential.payload.audience.contains(audience) &&
                    credential.payload.getClaim("code").asString().isNotEmpty()) {
                    // 检查role claim，允许二级邀请码（无role或role为空）和管理员密钥（role为admin-key）访问
                    val role = credential.payload.getClaim("role")?.asString()
                    if (role.isNullOrEmpty() || role == "admin-key") {
                        JWTPrincipal(credential.payload)
                    } else null
                } else null
            }
        }
    }
}
